{"$message_type":"diagnostic","message":"found invalid metadata files for crate `selectors` which `tauri_plugin` depends on","code":{"code":"E0786","explanation":"A metadata file was invalid.\n\nErroneous code example:\n\n```ignore (needs extern files)\nuse ::foo; // error: found invalid metadata files for crate `foo`\n```\n\nWhen loading crates, each crate must have a valid metadata file.\nInvalid files could be caused by filesystem corruption,\nan IO error while reading the file, or (rarely) a bug in the compiler itself.\n\nConsider deleting the file and recreating it,\nor reporting a bug against the compiler.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-store-2.3.0\\build.rs","byte_start":374,"byte_end":386,"line_start":23,"line_end":23,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    tauri_plugin::Builder::new(COMMANDS)","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"failed to mmap rmeta metadata: 'E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libselectors-cd363582a11380fc.rmeta'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"failed to mmap file 'E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libselectors-cd363582a11380fc.rlib': 页面文件太小，无法完成操作。 (os error 1455)","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0786]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: found invalid metadata files for crate `selectors` which `tauri_plugin` depends on\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-store-2.3.0\\build.rs:23:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tauri_plugin::Builder::new(COMMANDS)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: failed to mmap rmeta metadata: 'E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libselectors-cd363582a11380fc.rmeta'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: failed to mmap file 'E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libselectors-cd363582a11380fc.rlib': 页面文件太小，无法完成操作。 (os error 1455)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0786`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0786`.\u001b[0m\n"}
