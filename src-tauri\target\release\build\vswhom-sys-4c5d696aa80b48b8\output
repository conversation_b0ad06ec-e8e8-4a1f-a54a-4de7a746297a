cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=ext/vswhom.cpp
OPT_LEVEL = Some(0)
OUT_DIR = Some(E:\Seadragon\BMCTool\src-tauri\target\release\build\vswhom-sys-4c5d696aa80b48b8\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(E:\Seadragon\BMCTool\src-tauri\target\release\deps;E:\Seadragon\BMCTool\src-tauri\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;E:\Seadragon\BMCTool\node_modules\.bin;E:\Environment\nvm\v18.20.4\node_modules\pnpm\dist\node-gyp-bin;E:\Environment\Python39\Scripts\;E:\Environment\Python39\;C:\Program Files\Java\jdk-17.0.2\bin;d:\Program Files (x86)\cursor\resources\app\bin;d:\Program Files (x86)\cursor\resources\app\bin;C:\Program Files\Google\Chrome\Application;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Intel\DFPython\bin\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;E:\Environment\TortoiseSVN\bin;E:\Environment\apache-maven-3.9.2\bin;C:\usr;C:\Program Files\Mozilla Firefox;E:\Environment\allure-2.32.0\bin;E:\Environment\Git\cmd;E:\PyProjects\FruTool\venv\Lib\site-packages\qt5_applications\Qt\bin;C:\Program Files\dotnet\;C:\Program Files\Pandoc\;C:\Program Files\Mozilla Firefox;E:\Environment\Redis\;E:\Environment\nvm;E:\Environment\;E:\Environment\msys64\mingw64\bin;C:\Program Files\PowerShell\7\;E:\Environment\MySQL Server 5.7\bin;E:\Environment\cunzhi-cli-v0.3.8-windows-x86_64;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Mozilla Firefox;F:\工具\BrowserStackLocal\;E:\Environment\nvm;E:\Environment\nodejs;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;E:\Environment\Microsoft VS Code\bin;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
vswhom.cpp
ext/vswhom.cpp(213): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(216): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(431): warning C4456: declaration of 'hr' hides previous local declaration
ext/vswhom.cpp(418): note: see declaration of 'hr'
ext/vswhom.cpp(459): warning C4244: 'argument': conversion from 'LONGLONG' to 'int', possible loss of data
ext/vswhom.cpp(502): warning C4456: declaration of 'rc' hides previous local declaration
ext/vswhom.cpp(410): note: see declaration of 'rc'
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=vswhom
cargo:rustc-link-search=native=E:\Seadragon\BMCTool\src-tauri\target\release\build\vswhom-sys-4c5d696aa80b48b8\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-lib=dylib=OleAut32
cargo:rustc-link-lib=dylib=Ole32
