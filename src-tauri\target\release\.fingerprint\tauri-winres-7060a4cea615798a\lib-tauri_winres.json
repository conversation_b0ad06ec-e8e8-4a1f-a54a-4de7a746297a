{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2086567024422996381, "profile": 1369601567987815722, "path": 1793848224302803227, "deps": [[6493259146304816786, "indexmap", false, 12038930678525488807], [13443124389817561858, "embed_resource", false, 6955802052646798930], [15609422047640926750, "toml", false, 9929470609679616051]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winres-7060a4cea615798a\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}