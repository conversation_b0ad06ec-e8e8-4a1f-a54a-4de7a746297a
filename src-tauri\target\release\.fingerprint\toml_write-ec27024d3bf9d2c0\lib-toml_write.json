{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 15943748010645046320, "profile": 6243051256003197163, "path": 3864497108371988192, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\toml_write-ec27024d3bf9d2c0\\dep-lib-toml_write", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}