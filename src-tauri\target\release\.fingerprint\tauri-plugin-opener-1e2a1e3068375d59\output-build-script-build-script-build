{"$message_type":"diagnostic","message":"found possibly newer version of crate `aho_corasick` which `tauri_plugin` depends on","code":{"code":"E0460","explanation":"Found possibly newer version of crate `..` which `..` depends on.\n\nConsider these erroneous files:\n\n`a1.rs`\n```ignore (needs-linkage-with-other-tests)\n#![crate_name = \"a\"]\n\npub fn foo<T>() {}\n```\n\n`a2.rs`\n```ignore (needs-linkage-with-other-tests)\n#![crate_name = \"a\"]\n\npub fn foo<T>() {\n    println!(\"foo<T>()\");\n}\n```\n\n`b.rs`\n```ignore (needs-linkage-with-other-tests)\n#![crate_name = \"b\"]\n\nextern crate a; // linked with `a1.rs`\n\npub fn foo() {\n    a::foo::<isize>();\n}\n```\n\n`main.rs`\n```ignore (needs-linkage-with-other-tests)\nextern crate a; // linked with `a2.rs`\nextern crate b; // error: found possibly newer version of crate `a` which `b`\n                //        depends on\n\nfn main() {}\n```\n\nThe dependency graph of this program can be represented as follows:\n```text\n    crate `main`\n         |\n         +-------------+\n         |             |\n         |             v\ndepends: |         crate `b`\n `a` v1  |             |\n         |             | depends:\n         |             |  `a` v2\n         v             |\n      crate `a` <------+\n```\n\nCrate `main` depends on crate `a` (version 1) and crate `b` which in turn\ndepends on crate `a` (version 2); this discrepancy in versions cannot be\nreconciled. This difference in versions typically occurs when one crate is\ncompiled and linked, then updated and linked to another crate. The crate\n\"version\" is a SVH (Strict Version Hash) of the crate in an\nimplementation-specific way. Note that this error can *only* occur when\ndirectly compiling and linking with `rustc`; [Cargo] automatically resolves\ndependencies, without using the compiler's own dependency management that\ncauses this issue.\n\nThis error can be fixed by:\n * Using [Cargo], the Rust package manager, automatically fixing this issue.\n * Recompiling crate `a` so that both crate `b` and `main` have a uniform\n   version to depend on.\n\n[Cargo]: ../cargo/index.html\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-opener-2.4.0\\build.rs","byte_start":3962,"byte_end":3974,"line_start":116,"line_end":116,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    tauri_plugin::Builder::new(COMMANDS)","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"perhaps that crate needs to be recompiled?","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following crate versions were found:\ncrate `aho_corasick`: E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libaho_corasick-78ad8f1deecafa5a.rmeta\ncrate `aho_corasick`: E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libaho_corasick-78ad8f1deecafa5a.rlib\ncrate `tauri_plugin`: \\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libtauri_plugin-23571dd97dca5594.rlib","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0460]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: found possibly newer version of crate `aho_corasick` which `tauri_plugin` depends on\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-opener-2.4.0\\build.rs:116:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tauri_plugin::Builder::new(COMMANDS)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: perhaps that crate needs to be recompiled?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following crate versions were found:\u001b[0m\n\u001b[0m            crate `aho_corasick`: E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libaho_corasick-78ad8f1deecafa5a.rmeta\u001b[0m\n\u001b[0m            crate `aho_corasick`: E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libaho_corasick-78ad8f1deecafa5a.rlib\u001b[0m\n\u001b[0m            crate `tauri_plugin`: \\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\deps\\libtauri_plugin-23571dd97dca5594.rlib\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0460`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0460`.\u001b[0m\n"}
