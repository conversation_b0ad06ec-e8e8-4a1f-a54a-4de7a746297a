{"rustc": 1842507548689473721, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 15743642140631288636, "path": 16099854880971937190, "deps": [[4018467389006652250, "simd_adler32", false, 9996350304448225846], [7911289239703230891, "adler2", false, 1245032480037730250]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-4708de63a2623493\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}