{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 14724931235169721617, "path": 7171383704015662868, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\winnow-611a631cf89de829\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}