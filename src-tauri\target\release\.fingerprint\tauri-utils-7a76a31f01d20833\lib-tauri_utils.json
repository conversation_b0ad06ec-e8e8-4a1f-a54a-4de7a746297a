{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 14468088324988985849, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10269582393979280928], [3150220818285335163, "url", false, 3641681778539268415], [3191507132440681679, "serde_untagged", false, 1110498922304114690], [4071963112282141418, "serde_with", false, 17551515760566986380], [4899080583175475170, "semver", false, 5355439660870175787], [5986029879202738730, "log", false, 1001278331988017299], [6606131838865521726, "ctor", false, 7903081089341956288], [7170110829644101142, "json_patch", false, 16522623025774175484], [8319709847752024821, "uuid", false, 12889333317595018938], [9010263965687315507, "http", false, 14876794166302984965], [9451456094439810778, "regex", false, 1533732944023246879], [9556762810601084293, "brotli", false, 12716532641696030391], [9689903380558560274, "serde", false, 8256540449114535743], [10806645703491011684, "thiserror", false, 7687820832281580313], [11989259058781683633, "dunce", false, 1093578197412909291], [13625485746686963219, "anyhow", false, 10105933287001036773], [15367738274754116744, "serde_json", false, 15583641392441767829], [15609422047640926750, "toml", false, 5508104199339380821], [15622660310229662834, "walkdir", false, 1582080014897527364], [15932120279885307830, "memchr", false, 18426903225597535378], [17146114186171651583, "infer", false, 4088440672574051454], [17155886227862585100, "glob", false, 673696334861489311], [17186037756130803222, "phf", false, 14125509227224851775]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-7a76a31f01d20833\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}