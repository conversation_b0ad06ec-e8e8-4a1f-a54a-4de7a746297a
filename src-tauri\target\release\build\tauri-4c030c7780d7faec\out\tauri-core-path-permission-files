["\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\Seadragon\\BMCTool\\src-tauri\\target\\release\\build\\tauri-4c030c7780d7faec\\out\\permissions\\path\\autogenerated\\default.toml"]