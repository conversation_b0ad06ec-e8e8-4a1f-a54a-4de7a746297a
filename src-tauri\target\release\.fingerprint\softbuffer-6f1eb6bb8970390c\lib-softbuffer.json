{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 8790126463673744502, "deps": [[376837177317575824, "build_script_build", false, 12786865182366825647], [4143744114649553716, "raw_window_handle", false, 7964226615596664386], [5986029879202738730, "log", false, 1001278331988017299], [10281541584571964250, "windows_sys", false, 748400520035281689]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-6f1eb6bb8970390c\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}