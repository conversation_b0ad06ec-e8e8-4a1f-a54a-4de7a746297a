{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 14468088324988985849, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 4971980230356688490], [3060637413840920116, "proc_macro2", false, 7934411497656091993], [3150220818285335163, "url", false, 3951935901704706749], [3191507132440681679, "serde_untagged", false, 12800946814943634990], [4071963112282141418, "serde_with", false, 3964349396266844119], [4899080583175475170, "semver", false, 419261240759292608], [5986029879202738730, "log", false, 10532867000881244240], [6606131838865521726, "ctor", false, 7903081089341956288], [6913375703034175521, "schemars", false, 2446953744545836710], [7170110829644101142, "json_patch", false, 11228854024235000871], [8319709847752024821, "uuid", false, 15302500412365734880], [9010263965687315507, "http", false, 17110146251367972745], [9451456094439810778, "regex", false, 17646011235456479242], [9556762810601084293, "brotli", false, 6073424493124900376], [9689903380558560274, "serde", false, 10916219924335587096], [10806645703491011684, "thiserror", false, 11535494499749101502], [11655476559277113544, "cargo_metadata", false, 8654151030461173583], [11989259058781683633, "dunce", false, 15005223138484354296], [13625485746686963219, "anyhow", false, 10813515902240516357], [14232843520438415263, "html5ever", false, 9920235699688215785], [15088007382495681292, "kuchiki", false, 8617673498024573056], [15367738274754116744, "serde_json", false, 15030891084140450387], [15609422047640926750, "toml", false, 9929470609679616051], [15622660310229662834, "walkdir", false, 9402829855293401480], [15932120279885307830, "memchr", false, 12926780082988959665], [17146114186171651583, "infer", false, 3807487923387341649], [17155886227862585100, "glob", false, 9254835502858900043], [17186037756130803222, "phf", false, 9133923089564506149], [17990358020177143287, "quote", false, 3195255612779023861]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-63a08433d4d2bfb2\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}