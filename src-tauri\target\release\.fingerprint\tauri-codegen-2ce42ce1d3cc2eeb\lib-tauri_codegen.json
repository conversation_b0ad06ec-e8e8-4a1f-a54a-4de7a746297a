{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 8484181595138409276, "deps": [[2671782512663819132, "tauri_utils", false, 16513009898780546111], [3060637413840920116, "proc_macro2", false, 7934411497656091993], [3150220818285335163, "url", false, 3951935901704706749], [4899080583175475170, "semver", false, 419261240759292608], [4974441333307933176, "syn", false, 17600363626066887263], [7170110829644101142, "json_patch", false, 11228854024235000871], [7392050791754369441, "ico", false, 5667256755899430931], [8319709847752024821, "uuid", false, 15302500412365734880], [9556762810601084293, "brotli", false, 6073424493124900376], [9689903380558560274, "serde", false, 10916219924335587096], [9857275760291862238, "sha2", false, 10557122722166891727], [10806645703491011684, "thiserror", false, 11535494499749101502], [12687914511023397207, "png", false, 9524912147156034199], [13077212702700853852, "base64", false, 15848045384840014512], [15367738274754116744, "serde_json", false, 15030891084140450387], [15622660310229662834, "walkdir", false, 9402829855293401480], [17990358020177143287, "quote", false, 3195255612779023861]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-2ce42ce1d3cc2eeb\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}