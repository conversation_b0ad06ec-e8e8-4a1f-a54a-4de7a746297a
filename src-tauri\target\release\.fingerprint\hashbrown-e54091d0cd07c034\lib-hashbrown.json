{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 10396054020763142273, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-e54091d0cd07c034\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}