{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 12034658005281540297, "deps": [[2671782512663819132, "tauri_utils", false, 16513009898780546111], [4899080583175475170, "semver", false, 419261240759292608], [6913375703034175521, "schemars", false, 2446953744545836710], [7170110829644101142, "json_patch", false, 11228854024235000871], [9689903380558560274, "serde", false, 10916219924335587096], [12714016054753183456, "tauri_winres", false, 9029956362540969320], [13077543566650298139, "heck", false, 573122089029156912], [13475171727366188400, "cargo_toml", false, 15741880396629611769], [13625485746686963219, "anyhow", false, 10813515902240516357], [15367738274754116744, "serde_json", false, 15030891084140450387], [15609422047640926750, "toml", false, 9929470609679616051], [15622660310229662834, "walkdir", false, 9402829855293401480], [16928111194414003569, "dirs", false, 6183858853194766675], [17155886227862585100, "glob", false, 9254835502858900043]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-9b7ee7aef83bf292\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}